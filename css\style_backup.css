:root {
    --primary-color: #cc3333;
    --secondary-color: #2c3e50;
    --dark-color: #1a1a1a;
    --light-color: #f8f9fa;
    --text-color: #333;
    --overlay-color: rgba(204, 51, 51, 0.7);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    overflow-x: hidden;
}

/* Top Bar */
.top-bar {
    background: var(--primary-color);
    color: white;
    padding: 12px 0;
    font-size: 14px;
    border-bottom: 2px solid rgba(255,255,255,0.1);
}

.top-bar a {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
}

.top-bar a:hover {
    color: rgba(255,255,255,0.8);
}

.company-info {
    font-weight: 500;
    font-size: 15px;
}

.top-bar .contact-info {
    display: flex;
    align-items: center;
    gap: 25px;
    margin-right: 20px;
    flex-wrap: nowrap;
}

.top-bar .contact-info span {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    white-space: nowrap;
    flex-shrink: 0;
}

.contact-info {
    display: flex;
    align-items: center;
    gap: 25px;
    margin-right: 20px;
}

.contact-info span {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    white-space: nowrap;
}

.contact-info i {
    font-size: 12px;
    opacity: 0.9;
}

/* Language selector removed */

/* Navigation */
.navbar {
    background: white !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 15px 0;
    position: relative;
    z-index: 1000;
}

.navbar-brand img {
    height: 50px;
}

.navbar-nav .nav-link {
    color: var(--dark-color) !important;
    font-weight: 500;
    margin: 0 10px;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
}

/* Dropdown Menu */
.navbar-nav .dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    background: white;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-radius: 8px;
    padding: 10px 0;
    margin-top: 10px;
    min-width: 220px;
    z-index: 1000;
}

.dropdown-item {
    color: var(--text-color);
    padding: 12px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    background: none;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background: var(--light-color);
    color: var(--primary-color);
}

.dropdown-toggle::after {
    margin-left: 8px;
    transition: transform 0.3s ease;
}

.dropdown.show .dropdown-toggle::after {
    transform: rotate(180deg);
}

/* Hover dropdown functionality */
.navbar-nav .dropdown:hover .dropdown-menu,
.navbar-nav .dropdown.show .dropdown-menu {
    display: block !important;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.navbar-nav .dropdown .dropdown-menu {
    display: none;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.navbar-nav .dropdown:hover .dropdown-toggle::after,
.navbar-nav .dropdown.show .dropdown-toggle::after {
    transform: rotate(180deg);
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 600px;
    overflow: hidden;
}

.hero-section .carousel {
    height: 100%;
}

.hero-section .carousel-inner {
    height: 100%;
}

.hero-section .carousel-item {
    height: 100%;
    position: relative;
}

.hero-section .carousel-item img {
    height: 100%;
    object-fit: cover;
    filter: brightness(0.7);
}

.hero-section .carousel-caption {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    bottom: auto;
    transform: translateY(-50%);
    text-align: left;
    padding: 0;
    z-index: 10;
    max-width: none;
}

.carousel-content {
    text-align: left;
    max-width: 50%;
}

.carousel-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

.carousel-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    line-height: 1.6;
}

/* Responsive styles for carousel */
@media (max-width: 768px) {
    .hero-section {
        height: 400px;
    }

    .hero-section .carousel-caption {
        padding: 0 15px;
        top: 40%;
    }

    .carousel-content {
        max-width: 100%;
    }

    .carousel-content h1 {
        font-size: 2.2rem;
        margin-bottom: 1rem;
    }

    .carousel-content p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .btn-custom {
        padding: 12px 30px;
        font-size: 0.9rem;
    }
}

/* Old slider styles removed - now using Bootstrap carousel */

/* Old slide-content styles removed */

/* Old responsive styles removed */

.btn-custom {
    background-color: white;
    color: var(--primary-color);
    border: 2px solid white;
    padding: 15px 40px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 0;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
}

.btn-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-custom:hover::before {
    left: 100%;
}

.btn-custom:hover {
    background-color: transparent;
    color: white;
    border-color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

/* Old slider controls removed - using Bootstrap carousel controls */

/* Feature Cards */
.feature-section {
    margin-top: -100px;
    position: relative;
    z-index: 10;
}

.feature-section .row {
    margin: 0 -10px;
}

.feature-card {
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: white;
    padding: 2rem;
    text-align: left;
    transition: all 0.3s ease;
    height: 280px;
    border-radius: 8px;
    overflow: hidden;
    margin: 0;
}

.col-lg-3:nth-child(1) .feature-card {
    background-image: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%),
                      url('https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=300&fit=crop');
    background-blend-mode: overlay;
}

.col-lg-3:nth-child(2) .feature-card {
    background-image: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%),
                      url('https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=400&h=300&fit=crop');
    background-blend-mode: overlay;
}

.col-lg-3:nth-child(3) .feature-card {
    background-image: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%),
                      url('https://images.unsplash.com/photo-1565043666747-69f6646db940?w=400&h=300&fit=crop');
    background-blend-mode: overlay;
}

.col-lg-3:nth-child(4) .feature-card {
    background-image: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%),
                      url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop');
    background-blend-mode: overlay;
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1;
}

.card-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.feature-card .number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
    line-height: 1;
}

.feature-card h4 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: white;
}

.feature-card p {
    font-size: 0.95rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    color: white;
    line-height: 1.5;
}

.card-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    align-self: flex-start;
}

.card-icon i {
    font-size: 1.5rem;
    color: white;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.feature-card:hover .card-overlay {
    background: rgba(0, 0, 0, 0.5);
}

/* About Section */
.about-section {
    padding: 100px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.section-subtitle {
    color: var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.about-image {
    position: relative;
}

.about-image img {
    width: 100%;
    border-radius: 10px;
}

.experience-badge {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    padding: 20px;
    border-radius: 50%;
    text-align: center;
    width: 120px;
    height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.experience-badge .number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.experience-badge .text {
    font-size: 0.8rem;
    text-transform: uppercase;
}

/* Services Section */
.services-section {
    background: var(--primary-color);
    color: white;
    padding: 100px 0;
}

.service-card {
    position: relative;
    overflow: hidden;
    border-radius: 0;
    height: 300px;
    cursor: pointer;
}

.service-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-card:hover img {
    transform: scale(1.1);
}

.service-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.9));
    color: white;
    padding: 2rem;
    transform: translateY(60%);
    transition: transform 0.3s ease;
}

.service-card:hover .service-overlay {
    transform: translateY(0);
}

.service-overlay h4 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

/* Process Section */
.process-section {
    padding: 100px 0;
    background: var(--light-color);
}

.process-card {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.process-card:hover {
    transform: translateY(-10px);
}

.process-number {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 1.5rem;
}

.process-card h4 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

/* Responsive - consolidated mobile styles */
@media (max-width: 768px) {
    .feature-section {
        margin-top: 0;
        padding: 0 10px;
    }

    .feature-card {
        margin: 0 0 20px 0;
        height: 250px;
    }

    .feature-card .number {
        font-size: 2rem;
    }

    .feature-card h4 {
        font-size: 1.2rem;
    }

    .feature-card p {
        font-size: 0.9rem;
    }
}

    .about-section,
    .services-section,
    .process-section,
    .news-section {
        padding: 60px 0;
    }

    .top-bar {
        padding: 8px 0;
        font-size: 12px;
    }

    .company-info {
        font-size: 13px;
        margin-bottom: 10px;
    }

    .top-bar .contact-info {
        gap: 15px;
        margin-right: 0;
        margin-bottom: 10px;
        flex-wrap: nowrap;
    }

    .top-bar .contact-info span {
        font-size: 11px;
    }

    .contact-info {
        gap: 15px;
        margin-right: 0;
        margin-bottom: 10px;
    }

    .contact-info span {
        font-size: 11px;
    }

    /* Language selector removed */

    .news-card img {
        height: 180px;
    }

    .news-card-body {
        padding: 1.25rem;
    }

    /* Mobile dropdown adjustments */
    .navbar-collapse .dropdown-menu {
        position: static !important;
        transform: none !important;
        box-shadow: none;
        border: 1px solid #eee;
        margin-top: 0;
        background: var(--light-color);
    }

    /* Disable hover dropdown on mobile - use click instead */
    .navbar-collapse .navbar-nav .dropdown .dropdown-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
        transition: none;
        display: none;
    }

    .navbar-collapse .navbar-nav .dropdown.show .dropdown-menu {
        display: block;
    }

    /* Disable hover effect on mobile */
    .navbar-collapse .navbar-nav .dropdown:hover .dropdown-menu {
        display: none;
    }

    .navbar-collapse .navbar-nav .dropdown.show:hover .dropdown-menu {
        display: block;
    }
}

/* Contact Section */
.contact-section {
    background: linear-gradient(var(--overlay-color), var(--overlay-color)), url('../images/banner.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 100px 0;
}

.contact-section .contact-info {
    display: block;
    gap: 0;
    margin-right: 0;
}

.contact-section .contact-info .d-flex {
    display: flex !important;
    align-items: center;
    margin-bottom: 1rem;
}

.contact-section .contact-info span {
    display: inline;
    font-size: 1rem;
    white-space: normal;
}

.contact-form {
    background: rgba(255,255,255,0.1);
    padding: 3rem;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.form-control {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    border-radius: 0;
}

.form-control::placeholder {
    color: rgba(255,255,255,0.7);
}

.form-control:focus {
    background: rgba(255,255,255,0.2);
    border-color: white;
    color: white;
    box-shadow: none;
}

/* Footer */
.footer {
    background: var(--dark-color);
    color: white;
    padding: 3rem 0 1rem;
}

.footer h5 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.footer a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: var(--primary-color);
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    transition: background 0.3s ease;
}

.social-links a:hover {
    background: #c0392b;
}

/* News Section */
.news-section {
    padding: 100px 0;
    background: white;
}

.news-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.news-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.news-card-body {
    padding: 1.5rem;
}

.news-date {
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.news-card h5 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-weight: 600;
    line-height: 1.4;
}

.news-card p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.news-read-more {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.news-read-more:hover {
    color: #660000;
}

.news-read-more i {
    margin-left: 5px;
    transition: transform 0.3s ease;
}

.news-read-more:hover i {
    transform: translateX(3px);
}

/* Language switcher removed - Chinese only */
