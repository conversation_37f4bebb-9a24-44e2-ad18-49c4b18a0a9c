:root {
    --primary-color: #cc3333;
    --secondary-color: #2c3e50;
    --dark-color: #1a1a1a;
    --light-color: #f8f9fa;
    --text-color: #333;
    --overlay-color: rgba(204, 51, 51, 0.7);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    overflow-x: hidden;
    position: relative;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Mobile Menu Styles */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100%;
    background: white;
    z-index: 9999;
    transition: all 0.3s ease;
    overflow-y: auto;
    box-shadow: -5px 0 15px rgba(0,0,0,0.1);
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu .nav-item {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    position: relative;
}

.mobile-menu .dropdown-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mobile-menu .dropdown-toggle::after {
    display: inline-block;
    margin-left: 5px;
    transition: transform 0.3s ease;
    content: '\f078';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: var(--primary-color);
    font-size: 14px;
    margin-right: 10px;
}

.mobile-menu .dropdown-toggle.collapsed::after {
    transform: rotate(-90deg);
}

.mobile-menu .dropdown-menu {
    padding-left: 20px;
    display: none;
    background: #f9f9f9;
    position: static;
    border: none;
    box-shadow: none;
    margin-top: 0;
    transition: all 0.3s ease;
    padding: 0;
}

.mobile-menu .dropdown-menu.show {
    display: block;
    animation: slideDown 0.3s ease;
    background: #f5f5f5;
    border-radius: 4px;
    margin-top: 5px;
    padding: 10px 0;
}

.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 991.98px) {
    .navbar-collapse {
        display: none;
    }
    
    .mobile-menu-btn {
        display: block;
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
    }

}

.mobile-menu.active {
    right: 0;
}

.mobile-menu .nav-item {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.mobile-menu .dropdown-toggle::after {
    display: inline-block;
    margin-left: 5px;
    transition: transform 0.3s ease;
}

.mobile-menu .dropdown-toggle.collapsed::after {
    transform: rotate(-90deg);
}

.mobile-menu .dropdown-menu {
    padding-left: 20px;
    display: none;
}

.mobile-menu .dropdown-menu.show {
    display: block;
}

.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Top Bar */
.top-bar {
    background: var(--primary-color);
    color: white;
    padding: 12px 0;
    font-size: 14px;
    border-bottom: 2px solid rgba(255,255,255,0.1);
    position: relative;
    z-index: 2000;

    .language-selector {
        position: relative;
        z-index: 1001;

        .dropdown-toggle {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            border-radius: 20px;
            padding: 5px 15px;
            transition: all 0.3s ease;

            &:hover {
                background: rgba(255,255,255,0.2);
                transform: translateY(-2px);
            }

            i {
                margin-right: 5px;
            }
        }

        .dropdown-menu {
            border: none;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-top: 8px;
            min-width: 120px;

            .dropdown-item {
                padding: 8px 15px;
                color: var(--text-color);
                transition: all 0.2s ease;

                &:hover {
                    background: var(--light-color);
                    color: var(--primary-color);
                }
            }
        }
    }
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.top-bar a {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.top-bar a:hover {
    color: rgba(255,255,255,0.8);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.company-info {
    font-weight: 500;
    font-size: 15px;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.contact-info {
    align-items: center;
    gap: 25px;
    margin-right: 20px;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.contact-info span {
    display: inline;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    white-space: nowrap;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.contact-info i {
    font-size: 12px;
    opacity: 0.9;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Language selector removed */

/* Navigation */
.navbar {
    background: white !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 15px 0;
    position: relative;
    z-index: 1000;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.navbar-brand img {
    height: 50px;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.navbar-nav .nav-link {
    color: var(--dark-color) !important;
    font-weight: 500;
    margin: 0 10px;
    transition: color 0.3s ease;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Dropdown Menu */
.dropdown-menu {
    background: white;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-radius: 0;
    padding: 10px 0;
    margin-top: 10px;
    min-width: 220px;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.dropdown-item {
    color: var(--text-color);
    padding: 12px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    background: none;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.dropdown-item:hover,
.dropdown-item:focus {
    background: var(--light-color);
    color: var(--primary-color);
}
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

.dropdown-toggle::after {
    margin-left: 8px;
    transition: transform 0.3s ease;
}
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}


.dropdown.show .dropdown-toggle::after {
    transform: rotate(180deg);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Hover dropdown functionality */
.navbar-nav .dropdown:hover .dropdown-menu {
    display: block;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.navbar-nav .dropdown .dropdown-menu {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    display: block;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.navbar-nav .dropdown:hover .dropdown-toggle::after {
    transform: rotate(180deg);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Hero Section */
.hero-section {
    position: relative;
    height: 800px;
    overflow: hidden;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.hero-section .carousel {
    height: 100%;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.hero-section .carousel-inner {
    height: 100%;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.hero-section .carousel-item {
    height: 100%;
    position: relative;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.hero-section .carousel-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(153, 0, 0, 0.6);
    z-index: 1;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.hero-section .carousel-item img {
    height: 100%;
    object-fit: cover;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}



.hero-section .carousel-caption {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    bottom: auto;
    transform: translateY(-50%);
    text-align: left;
    padding: 0;
    z-index: 10;
    max-width: none;
    pointer-events: none;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.hero-section .carousel-caption .carousel-content {
    pointer-events: auto;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.carousel-content {
    text-align: left;
    max-width: 50%;
    pointer-events: auto;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.carousel-content .btn {
    pointer-events: auto;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.carousel-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.carousel-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    line-height: 1.6;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Responsive styles for carousel */
@media (max-width: 768px) {
    .hero-section {
        height: 400px;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    .hero-section .carousel-caption {
        padding: 0 15px;
        top: 40%;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    .carousel-content {
        max-width: 100%;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    .carousel-content h1 {
        font-size: 2.2rem;
        margin-bottom: 1rem;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    .carousel-content p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    .btn-custom {
        padding: 12px 30px;
        font-size: 0.9rem;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Old slider styles removed - now using Bootstrap carousel */

/* Old slide-content styles removed */

/* Old responsive styles removed */

.btn-custom {
    background-color: white;
    color: var(--primary-color);
    border: 2px solid white;
    padding: 15px 40px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-radius: 0;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.btn-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.btn-custom:hover::before {
    left: 100%;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.btn-custom:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Carousel Controls */
.hero-section .carousel-control-prev,
.hero-section .carousel-control-next {
    z-index: 15;
    width: 5%;
    opacity: 0.8;
    transition: opacity 0.3s ease;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.hero-section .carousel-control-prev:hover,
.hero-section .carousel-control-next:hover {
    opacity: 1;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.hero-section .carousel-control-prev-icon,
.hero-section .carousel-control-next-icon {
    width: 2rem;
    height: 2rem;
    background-size: 100%, 100%;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Ensure controls are clickable */
.hero-section .carousel-control-prev,
.hero-section .carousel-control-next {
    pointer-events: auto;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Feature Cards */
.feature-section {
    margin-top: -100px;
    position: relative;
    z-index: 10;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.feature-section .row {
    margin: 0 -10px;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.feature-card {
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: white;
    padding: 2rem;
    text-align: left;
    transition: all 0.08s ease-out;
    height: 280px;
    border-radius: 0;
    overflow: hidden;
    margin: 0;
    cursor: pointer;
    top: 0;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: inherit;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transition: transform 0.08s ease;
    z-index: -1;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.col-lg-3:nth-child(1) .feature-card::before {
    background-image: url('https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=400&fit=crop');
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.col-lg-3:nth-child(2) .feature-card::before {
    background-image: url('https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=600&h=400&fit=crop');
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.col-lg-3:nth-child(3) .feature-card::before {
    background-image: url('https://images.unsplash.com/photo-1565043666747-69f6646db940?w=600&h=400&fit=crop');
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.col-lg-3:nth-child(4) .feature-card::before {
    background-image: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=400&fit=crop');
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.col-lg-3:nth-child(1) .feature-card,
.col-lg-3:nth-child(2) .feature-card,
.col-lg-3:nth-child(3) .feature-card,
.col-lg-3:nth-child(4) .feature-card {
    background-image: linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(45, 45, 45, 0.8) 100%);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1;
    transition: all 0.3s ease-out;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.card-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.feature-card .number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
    line-height: 1;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.feature-card h4 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: white;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.feature-card p {
    font-size: 0.95rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    color: white;
    line-height: 1.5;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.card-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    align-self: flex-start;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.card-icon i {
    font-size: 1.5rem;
    color: white;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.feature-card:hover {
    transition: all 0.08s ease-out;
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.feature-card:hover::before {
    transform: scale(1.1);

.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.feature-card:hover .card-overlay {
    background: rgba(0, 0, 0, 0.5);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* About Section */
.about-section {
    padding: 100px 0;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--secondary-color);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.section-subtitle {
    color: var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.about-image {
    position: relative;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.about-image img {
    width: 100%;
    border-radius: 0;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.experience-badge {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    padding: 20px;
    border-radius: 50%;
    text-align: center;
    width: 120px;
    height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.experience-badge .number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.experience-badge .text {
    font-size: 0.8rem;
    text-transform: uppercase;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Services Section */
.services-section {
    background: linear-gradient(rgba(153, 0, 0, 0.8), rgba(153, 0, 0, 0.8)), url('https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    background-repeat: no-repeat;
    color: white;
    padding: 100px 0;
    position: relative;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.service-card {
    position: relative;
    overflow: hidden;
    border-radius: 0;
    height: 300px;
    cursor: pointer;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.service-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.service-card:hover img {
    transform: scale(1.1);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.service-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.9));
    color: white;
    padding: 1.5rem 2rem 0rem 2rem;
    transform: translateY(50%);
    transition: transform 0.3s ease;
    height: 125px;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.service-card:hover .service-overlay {
    transform: translateY(0);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}
 
.service-overlay h4 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Products Section */
.products-section {
    background: white;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.product-categories {
    margin-bottom: 3rem;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.category-btn {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 12px 24px;
    margin: 0 10px 10px 0;
    border-radius: 30px;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.category-btn:hover,
.category-btn.active {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem 0;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem 0;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.product-image {
    width: 100%;
    height: 250px;
    overflow: hidden;
    position: relative;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4% 0;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem 0;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.product-card:hover .product-image img {
    transform: scale(1.1);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem 0;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.product-content {
    padding: 2rem;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.product-content h4 {
    color: var(--secondary-color);
    font-size: 1.4rem;
    margin-bottom: 1rem;
    font-weight: 600;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.product-content p {
    color: var(--text-color);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}



.btn-more {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.btn-more:hover {
    color: var(--secondary-color);
    transform: translateX(5px);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.btn-more::after {
    content: '→';
    margin-left: 8px;
    transition: transform 0.3s ease;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.btn-more:hover::after {
    transform: translateX(3px);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.product-item {
    transition: all 0.5s ease;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.product-item.fade-out {
    opacity: 0;
    transform: scale(0.8);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.product-item.fade-in {
    opacity: 1;
    transform: scale(1);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.back-to-top:hover {
    background: var(--secondary-color);
    transform: translateY(0);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.back-to-top:active {
    transform: scale(0.95);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Product Detail Page Styles */
.thumbnail-img {
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.3s ease;
    border: 2px solid transparent;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.thumbnail-img:hover,
.thumbnail-img.active {
    opacity: 1;
    border-color: var(--primary-color);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.product-info .product-name {
    color: var(--secondary-color);
    font-weight: 700;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.product-features ul li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.product-features ul li:last-child {
    border-bottom: none;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.technical-specs .table td {
    padding: 12px 15px;
    vertical-align: middle;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.action-buttons .btn {
    min-width: 160px;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Breadcrumb Styles */
.breadcrumb-section {
    position: relative;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.breadcrumb-section {
    position: relative;
    z-index: 1;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.breadcrumb-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6); /* 黑色半透明蒙版，透明度80% */
    z-index: -1;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}


.breadcrumb-section .breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: white;}
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

.breadcrumb-item+.breadcrumb-item::before {   color: white;}
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}


.breadcrumb-section .breadcrumb a {
    text-decoration: none;
    transition: color 0.3s ease;
    color: white; /* 链接颜色改为白色 */
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.breadcrumb-section .breadcrumb a:hover {
    color: white !important;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.breadcrumb-section h1,
.breadcrumb-section p {
    color: white;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}


/* Process Section */
.process-section {
    padding: 100px 0;
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1565043666747-69f6646db940?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    background-repeat: no-repeat;
    color: white;
    position: relative;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.process-section .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.process-section .section-title {
    color: white;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.process-section .lead {
    color: rgba(255, 255, 255, 0.9);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.process-card {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 0;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    height: 100%;
    color: var(--text-color);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.process-card h4 {
    color: var(--text-color);
    margin-bottom: 1rem;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.process-card p {
    color: #999;
    margin-bottom: 0.5rem;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.process-card:hover {
    transform: translateY(-10px);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.process-list {
    list-style: none;
    padding: 0;
    margin: 1rem 0 0 0;
    text-align: left;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.process-list li {
    position: relative;
    padding: 0.25rem 0 0.25rem 1.5rem;
    margin-bottom: 0.3rem;
    color: #666;
    font-size: 0.95rem;
    line-height: 1.4;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.process-list li:before {
    content: "✓";
    position: absolute;
    left: 0;
    top: 0.25rem;
    color: var(--primary-color);
    font-size: 1rem;
    font-weight: bold;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.process-list li:last-child {
    margin-bottom: 0;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.process-number {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 1rem;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.process-stage {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Responsive - consolidated mobile styles */
@media (max-width: 768px) {
    .feature-section {
        margin-top: 0;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    .about-section,
    .services-section,
    .process-section,
    .news-section {
        padding: 60px 0;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    .top-bar {
        padding: 8px 0;
        font-size: 12px;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    .company-info {
        font-size: 13px;
        margin-bottom: 10px;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    .contact-info {
        flex-direction: column;
        gap: 8px;
        margin-right: 0;
        margin-bottom: 10px;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    .contact-info span {
        font-size: 12px;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    /* Language selector removed */

    .news-card img {
        height: 180px;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    .news-card-body {
        padding: 1.25rem;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    /* Process section mobile adjustments */
    .process-card {
        margin-bottom: 2rem;
        padding: 1.5rem;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    .process-number {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: 0.8rem;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    .process-stage {
        font-size: 1rem;
        margin-bottom: 0.4rem;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    .process-list li {
        font-size: 0.9rem;
        padding: 0.2rem 0 0.2rem 1.2rem;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    /* Button adjustments for mobile */
    .btn-custom {
        padding: 10px 25px;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

    /* Background attachment fix for mobile */
    .services-section,
    .process-section,
    .contact-section {
        background-attachment: scroll;
    .lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Contact Section */
.contact-section {
    background: linear-gradient(rgba(153, 0, 0, 0.7), rgba(153, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1423666639041-f56000c27a9a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    background-repeat: no-repeat;
    color: white;
    padding: 100px 0;
    position: relative;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}



.contact-form {
    background: rgba(255,255,255,0.1);
    padding: 3rem;
    border-radius: 0;
    backdrop-filter: blur(10px);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.form-control {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    border-radius: 0;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.form-control::placeholder {
    color: rgba(255,255,255,0.7);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.form-control:focus {
    background: rgba(255,255,255,0.2);
    border-color: white;
    color: white;
    box-shadow: none;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Footer */
.footer {
    background: var(--dark-color);
    color: white;
    padding: 3rem 0 1rem;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.footer h5 {
    color: var(--primary-color);
    margin-bottom: 1rem;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.footer a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.footer a:hover {
    color: var(--primary-color);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    transition: background 0.3s ease;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.social-links a:hover {
    background: #c0392b;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* News Section */
.news-section {
    padding: 100px 0;
    background: white;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.news-card {
    background: white;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.news-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.news-card-body {
    padding: 1.5rem;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.news-date {
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.news-card h5 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-weight: 600;
    line-height: 1.4;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.news-card p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1rem;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.news-read-more {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: color 0.3s ease;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.news-read-more:hover {
    color: #660000;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.news-read-more i {
    margin-left: 5px;
    transition: transform 0.3s ease;
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

.news-read-more:hover i {
    transform: translateX(3px);
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

}

/* Language switcher removed - Chinese only */