<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品详情 - 武汉市金鸿祺科技有限公司</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation CSS -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top" style="background: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <div class="container">
            <a class="navbar-brand" href="index.html" style="font-weight: bold; color: var(--secondary-color); font-size: 1.5rem;">
                <i class="fas fa-industry me-2" style="color: var(--primary-color);"></i>
                金鸿祺科技
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="products.html">产品中心</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="service.html">解决方案</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="news.html">新闻资讯</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">关于我们</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">联系我们</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <section class="breadcrumb-section" style="padding: 120px 0 60px; background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%); color: white;">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <nav aria-label="breadcrumb" data-aos="fade-up">
                        <ol class="breadcrumb mb-3" style="background: transparent;">
                            <li class="breadcrumb-item"><a href="index.html" style="color: rgba(255,255,255,0.8);">首页</a></li>
                            <li class="breadcrumb-item"><a href="products.html" style="color: rgba(255,255,255,0.8);">新闻中心</a></li>
                            <li class="breadcrumb-item active" aria-current="page" style="color: white;">新闻详情</li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-3" data-aos="fade-up" data-aos-delay="100" id="product-title">新闻中心</h1>
                    <p class="lead mb-0" data-aos="fade-up" data-aos-delay="200">专业焊接设备，为您提供高效可靠的焊接解决方案</p>
                </div>
            </div>
        </div>
    </section>

    <!---新闻详情页面start-->
    <section class="news-detail-section" style="padding: 80px 0;">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <!-- 新闻详情卡片 -->
                    <div class="news-detail-card" style="background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden;" data-aos="fade-up">
                        <!-- 新闻头部信息 -->
                        <div class="news-header" style="padding: 40px 40px 20px;">
                            <h1 class="news-title" style="font-size: 2.2rem; font-weight: bold; color: var(--secondary-color); line-height: 1.4; margin-bottom: 20px;">
                                智能焊接机器人技术在汽车制造业的应用与发展前景
                            </h1>

                            <!-- 新闻元信息 -->
                            <div class="news-meta" style="display: flex; flex-wrap: wrap; gap: 20px; padding: 15px 0; border-bottom: 1px solid #eee; margin-bottom: 30px;">
                                <div class="meta-item" style="display: flex; align-items: center; color: #666;">
                                    <i class="fas fa-user me-2" style="color: var(--primary-color);"></i>
                                    <span class="meta-label" style="font-weight: 500;">作者：</span>
                                    <span class="meta-value">技术部</span>
                                </div>
                                <div class="meta-item" style="display: flex; align-items: center; color: #666;">
                                    <i class="fas fa-calendar-alt me-2" style="color: var(--primary-color);"></i>
                                    <span class="meta-label" style="font-weight: 500;">发布时间：</span>
                                    <span class="meta-value">2024-01-15</span>
                                </div>
                                <div class="meta-item" style="display: flex; align-items: center; color: #666;">
                                    <i class="fas fa-eye me-2" style="color: var(--primary-color);"></i>
                                    <span class="meta-label" style="font-weight: 500;">访问量：</span>
                                    <span class="meta-value">1,256</span>
                                </div>
                            </div>
                        </div>

                        <!-- 新闻内容 -->
                        <div class="news-content" style="padding: 0 40px 40px;">
                            <!-- 新闻图片 -->
                            <div class="news-image mb-4">
                                <img src="images/news-detail-1.jpg" alt="智能焊接机器人" class="img-fluid" style="width: 100%; border-radius: 10px;">
                            </div>

                            <!-- 新闻正文 -->
                            <div class="news-text" style="font-size: 1.1rem; line-height: 1.8; color: #444;">
                                <p>随着工业4.0时代的到来，智能制造技术正在深刻改变着传统制造业的生产模式。在汽车制造领域，智能焊接机器人技术作为关键的自动化设备，正发挥着越来越重要的作用。</p>

                                <h3 style="color: var(--secondary-color); margin: 30px 0 20px; font-size: 1.4rem;">技术优势</h3>
                                <p>智能焊接机器人相比传统人工焊接具有以下显著优势：</p>
                                <ul style="margin: 20px 0; padding-left: 20px;">
                                    <li>精度高：焊接精度可达±0.1mm，确保焊接质量的一致性</li>
                                    <li>效率高：24小时连续作业，生产效率提升300%以上</li>
                                    <li>安全性：减少人工操作风险，改善作业环境</li>
                                    <li>成本低：长期使用成本远低于人工焊接</li>
                                </ul>

                                <h3 style="color: var(--secondary-color); margin: 30px 0 20px; font-size: 1.4rem;">应用场景</h3>
                                <p>在汽车制造过程中，智能焊接机器人主要应用于以下场景：</p>
                                <p>车身焊接、底盘组装、零部件连接等关键工艺环节。通过精确的路径规划和焊接参数控制，确保每一个焊点都达到最佳质量标准。</p>

                                <h3 style="color: var(--secondary-color); margin: 30px 0 20px; font-size: 1.4rem;">发展前景</h3>
                                <p>未来，随着人工智能、机器学习等技术的不断发展，智能焊接机器人将具备更强的自适应能力和学习能力，能够应对更加复杂多变的焊接任务，为汽车制造业的智能化转型提供强有力的技术支撑。</p>
                            </div>
                        </div>
                    </div>

                    <!-- 上一篇/下一篇导航 -->
                    <div class="news-navigation" style="margin-top: 40px;" data-aos="fade-up" data-aos-delay="200">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="nav-item prev-news" style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); transition: all 0.3s ease;">
                                    <div class="nav-label" style="color: #999; font-size: 0.9rem; margin-bottom: 8px;">
                                        <i class="fas fa-chevron-left me-2"></i>上一篇
                                    </div>
                                    <a href="#" class="nav-title" style="color: var(--secondary-color); text-decoration: none; font-weight: 500; display: block; line-height: 1.4;">
                                        新能源汽车焊接技术的创新与挑战
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="nav-item next-news" style="background: white; border-radius: 10px; padding: 20px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); transition: all 0.3s ease; text-align: right;">
                                    <div class="nav-label" style="color: #999; font-size: 0.9rem; margin-bottom: 8px;">
                                        下一篇<i class="fas fa-chevron-right ms-2"></i>
                                    </div>
                                    <a href="#" class="nav-title" style="color: var(--secondary-color); text-decoration: none; font-weight: 500; display: block; line-height: 1.4;">
                                        激光焊接设备在精密制造中的应用
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!---新闻详情页面end-->

    <!-- Footer -->
    <footer style="background: var(--secondary-color); color: white; padding: 60px 0 30px;">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5 class="mb-3">武汉市金鸿祺科技有限公司</h5>
                    <p class="mb-3">专业从事汽车焊装设备的研发、生产和销售，为客户提供全方位的焊接解决方案。</p>
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-weixin"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-weibo"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-qq"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">快速链接</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.html" class="text-white-50">首页</a></li>
                        <li><a href="products.html" class="text-white-50">产品中心</a></li>
                        <li><a href="service.html" class="text-white-50">解决方案</a></li>
                        <li><a href="about.html" class="text-white-50">关于我们</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="mb-3">联系信息</h6>
                    <p class="mb-2"><i class="fas fa-map-marker-alt me-2"></i>武汉市东西湖区</p>
                    <p class="mb-2"><i class="fas fa-phone me-2"></i>027-8888-8888</p>
                    <p class="mb-2"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="mb-3">服务时间</h6>
                    <p class="mb-2">周一至周五：8:00-18:00</p>
                    <p class="mb-2">周六：9:00-17:00</p>
                    <p class="mb-0">周日：休息</p>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 武汉市金鸿祺科技有限公司. 保留所有权利.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-white-50 me-3">隐私政策</a>
                    <a href="#" class="text-white-50">使用条款</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTop" class="back-to-top" title="返回顶部">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- Custom JS -->
    <script src="css/script.js"></script>

    <!-- 新闻详情页面自定义样式 -->
    <style>
        .news-navigation .nav-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
        }

        .news-navigation .nav-title:hover {
            color: var(--primary-color) !important;
        }

        .news-meta .meta-item {
            transition: all 0.3s ease;
        }

        .news-meta .meta-item:hover {
            color: var(--primary-color) !important;
        }

        .news-content img {
            transition: transform 0.3s ease;
        }

        .news-content img:hover {
            transform: scale(1.02);
        }

        @media (max-width: 768px) {
            .news-header {
                padding: 30px 20px 15px !important;
            }

            .news-content {
                padding: 0 20px 30px !important;
            }

            .news-title {
                font-size: 1.8rem !important;
            }

            .news-meta {
                flex-direction: column;
                gap: 10px !important;
            }

            .news-navigation .nav-item {
                text-align: left !important;
            }
        }
    </style>

</body>
</html>
