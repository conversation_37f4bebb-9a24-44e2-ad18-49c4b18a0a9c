:root {
    --primary-color: #cc3333;
    --secondary-color: #2c3e50;
    --dark-color: #1a1a1a;
    --light-color: #f8f9fa;
    --text-color: #333;
    --overlay-color: rgba(204, 51, 51, 0.7);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    overflow-x: hidden;
    position: relative;
}

/* Navigation Styles */
.navbar {
    background: white !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 15px 0;
    position: relative;
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--secondary-color) !important;
}

.navbar-brand img {
    height: 50px;
}

.navbar-nav .nav-link {
    color: var(--dark-color) !important;
    font-weight: 500;
    margin: 0 10px;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
}

/* Dropdown Styles */
.navbar-nav .dropdown:hover .dropdown-menu {
    display: block;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.navbar-nav .dropdown .dropdown-menu {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    display: block;
}

.navbar-nav .dropdown:hover .dropdown-toggle::after {
    transform: rotate(180deg);
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 800px;
    overflow: hidden;
}

.hero-section .carousel {
    height: 100%;
}

.hero-section .carousel-inner {
    height: 100%;
}

.hero-section .carousel-item {
    height: 100%;
    position: relative;
}

.hero-section .carousel-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(153, 0, 0, 0.6);
    z-index: 1;
}

.hero-section .carousel-item img {
    height: 100%;
    object-fit: cover;
}

.hero-section .carousel-caption {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    bottom: auto;
    transform: translateY(-50%);
    text-align: left;
    padding: 0;
    z-index: 10;
    max-width: none;
    pointer-events: none;
}

.hero-section .carousel-caption .carousel-content {
    pointer-events: auto;
}

/* Carousel Controls */
.hero-section .carousel-control-prev,
.hero-section .carousel-control-next {
    z-index: 15;
    width: 5%;
    opacity: 0.8;
    transition: opacity 0.3s ease;
    pointer-events: auto;
}

.hero-section .carousel-control-prev:hover,
.hero-section .carousel-control-next:hover {
    opacity: 1;
}

.hero-section .carousel-control-prev-icon,
.hero-section .carousel-control-next-icon {
    width: 2rem;
    height: 2rem;
    background-size: 100%, 100%;
}

.hero-content {
    text-align: center;
    color: white;
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: fadeInUp 1s ease;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    animation: fadeInUp 1s ease 0.2s both;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease 0.4s both;
}

.btn-hero {
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.btn-hero-primary {
    background: var(--primary-color);
    color: white;
    border: 2px solid var(--primary-color);
}

.btn-hero-primary:hover {
    background: transparent;
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(220, 53, 69, 0.3);
}

.btn-hero-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-hero-secondary:hover {
    background: white;
    color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.3);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Section Styles */
.section-subtitle {
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 0.5rem;
}

.section-title {
    color: var(--secondary-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

/* Business Cards */
.business-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid transparent;
    height: 100%;
}

.business-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    border-color: var(--primary-color);
}

.business-card .icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.business-card:hover .icon {
    transform: scale(1.1);
    box-shadow: 0 10px 25px rgba(220, 53, 69, 0.3);
}

.business-card h4 {
    color: var(--secondary-color);
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.business-card p {
    color: var(--text-color);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.business-card .btn-more {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.business-card .btn-more:hover {
    color: var(--secondary-color);
    transform: translateX(5px);
}

.business-card .btn-more::after {
    content: '→';
    margin-left: 8px;
    transition: transform 0.3s ease;
}

.business-card .btn-more:hover::after {
    transform: translateX(3px);
}

/* Service Cards */
.service-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.service-image {
    width: 100%;
    height: 250px;
    overflow: hidden;
    position: relative;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-card:hover .service-image img {
    transform: scale(1.1);
}

.service-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, var(--overlay-color));
    color: white;
    padding: 1.5rem 2rem 2.5rem 2rem;
    transform: translateY(50%);
    transition: transform 0.3s ease;
    height: 125px;
}

.service-card:hover .service-overlay {
    transform: translateY(0);
}

.service-overlay h4 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.service-overlay p {
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.4;
}

/* Products Section */
.products-section {
    background: white;
}

.product-categories {
    margin-bottom: 3rem;
}

.category-btn {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 12px 24px;
    margin: 0 10px 10px 0;
    border-radius: 30px;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
}

.category-btn:hover,
.category-btn.active {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.product-image {
    width: 100%;
    height: 250px;
    overflow: hidden;
    position: relative;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-content {
    padding: 2rem;
}

.product-content h4 {
    color: var(--secondary-color);
    font-size: 1.4rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.product-content p {
    color: var(--text-color);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.btn-more {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.btn-more:hover {
    color: var(--secondary-color);
    transform: translateX(5px);
}

.btn-more::after {
    content: '→';
    margin-left: 8px;
    transition: transform 0.3s ease;
}

.btn-more:hover::after {
    transform: translateX(3px);
}

.product-item {
    transition: all 0.5s ease;
}

.product-item.fade-out {
    opacity: 0;
    transform: scale(0.8);
}

.product-item.fade-in {
    opacity: 1;
    transform: scale(1);
}

/* Process Section */
.process-section {
    background: var(--light-color);
    padding: 100px 0;
}

.process-step {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
}

.process-step:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.process-step .step-number {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.process-step:hover .step-number {
    transform: scale(1.1);
    box-shadow: 0 10px 25px rgba(220, 53, 69, 0.3);
}

.process-step h4 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-weight: 600;
    font-size: 1.3rem;
}

.process-step p {
    color: var(--text-color);
    line-height: 1.6;
}

/* News Section */
.news-section {
    background: white;
}

.news-categories {
    margin-bottom: 3rem;
}

.news-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.news-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.news-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover img {
    transform: scale(1.1);
}

.news-card-body {
    padding: 1.5rem;
}

.news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.news-date {
    color: var(--text-color);
    font-size: 0.9rem;
    opacity: 0.7;
}

.news-category {
    background: var(--primary-color);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.news-card h5 {
    color: var(--secondary-color);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.news-card p {
    color: var(--text-color);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.news-read-more {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}

.news-read-more:hover {
    color: var(--secondary-color);
    transform: translateX(5px);
}

.news-read-more i {
    margin-left: 8px;
    transition: transform 0.3s ease;
}

.news-read-more:hover i {
    transform: translateX(3px);
}

.news-item {
    transition: all 0.5s ease;
}

.news-item.fade-out {
    opacity: 0;
    transform: scale(0.8);
}

.news-item.fade-in {
    opacity: 1;
    transform: scale(1);
}

/* Mobile Menu Styles */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100%;
    background: white;
    z-index: 9999;
    transition: all 0.3s ease;
    overflow-y: auto;
    box-shadow: -5px 0 15px rgba(0,0,0,0.1);
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu .nav-item {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    position: relative;
}

.mobile-menu .nav-item:hover {
    background: var(--light-color);
}

.mobile-menu .nav-link {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    display: block;
}

.mobile-menu .nav-link.active {
    color: var(--primary-color);
}

.mobile-menu .dropdown-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mobile-menu .dropdown-toggle::after {
    display: inline-block;
    margin-left: 5px;
    transition: transform 0.3s ease;
    content: '\f078';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: var(--primary-color);
    font-size: 14px;
    margin-right: 10px;
}

.mobile-menu .dropdown-toggle.collapsed::after {
    transform: rotate(-90deg);
}

.mobile-menu .dropdown-menu {
    padding-left: 20px;
    display: none;
    background: #f9f9f9;
    position: static;
    border: none;
    box-shadow: none;
    margin-top: 0;
    transition: all 0.3s ease;
    padding: 0;
}

.mobile-menu .dropdown-menu.show {
    display: block;
    animation: slideDown 0.3s ease;
    background: #f5f5f5;
    border-radius: 4px;
    margin-top: 5px;
    padding: 10px 0;
}

.mobile-menu-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-color);
    cursor: pointer;
}

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.back-to-top:hover {
    background: var(--secondary-color);
    transform: translateY(0);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:active {
    transform: scale(0.95);
}

/* Pagination Styles */
.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

/* Product Detail Page Styles */
.thumbnail-img {
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.thumbnail-img:hover,
.thumbnail-img.active {
    opacity: 1;
    border-color: var(--primary-color);
}

.product-info .product-name {
    color: var(--secondary-color);
    font-weight: 700;
}

.product-features ul li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.product-features ul li:last-child {
    border-bottom: none;
}

.technical-specs .table td {
    padding: 12px 15px;
    vertical-align: middle;
}

.action-buttons .btn {
    min-width: 160px;
}

/* Breadcrumb Styles */
.breadcrumb-section .breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: rgba(255,255,255,0.6);
}

.breadcrumb-section .breadcrumb a {
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-section .breadcrumb a:hover {
    color: white !important;
}

/* Footer Styles */
.footer {
    background: var(--dark-color);
    color: rgba(255,255,255,0.8);
    padding: 3rem 0 1rem;
}

.footer h5 {
    color: white;
    margin-bottom: 1rem;
}

.footer a {
    color: rgba(255,255,255,0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: var(--primary-color);
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.1);
    color: white;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    transition: background 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
}

/* Form Styles */
.form-control {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 12px 15px;
    transition: border-color 0.3s ease;
}

.form-control::placeholder {
    color: #999;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Special AOS Animation Styles */
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

/* Mobile Navigation */
@media (max-width: 991.98px) {
    .navbar-collapse {
        display: none;
    }

    .mobile-menu-btn {
        display: block;
        background: none;
        border: none;
        color: var(--dark-color);
        font-size: 24px;
        cursor: pointer;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        height: 400px;
    }

    .hero-section .carousel-caption {
        padding: 0 15px;
        top: 40%;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .section-title {
        font-size: 2rem;
    }

    .category-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
        margin: 0 5px 10px 0;
    }

    .product-content h4,
    .news-card h5 {
        font-size: 1.2rem;
    }

    .navbar-nav .nav-link {
        margin: 5px 0;
    }

    .business-card .icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .process-step .step-number {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}
