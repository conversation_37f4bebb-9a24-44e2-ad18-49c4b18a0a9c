:root {
    --primary-color: #cc3333;
    --secondary-color: #2c3e50;
    --dark-color: #1a1a1a;
    --light-color: #f8f9fa;
    --text-color: #333;
    --overlay-color: rgba(204, 51, 51, 0.7);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    overflow-x: hidden;
    position: relative;
}

 
.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}

/* Mobile Menu Styles */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100%;
}


.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}



body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    overflow-x: hidden;
    position: relative;
}



.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}


/* Mobile Menu Styles */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -350px;
    width: 350px;
    height: 100%;
    background: white;
    z-index: 9999;
    transition: all 0.3s ease;
    overflow-y: auto;
    box-shadow: -5px 0 15px rgba(0,0,0,0.1);
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu .nav-item {
    padding: 5px 0px;
    border-bottom: 1px solid #eee;
    position: relative;
}

.mobile-menu .dropdown-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mobile-menu .dropdown-toggle::after {
    display: inline-block;
    margin-left: 5px;
    transition: transform 0.3s ease;
    content: '\f078';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    color: var(--primary-color);
    font-size: 14px;
    margin-right: 10px;
}

.mobile-menu .dropdown-toggle.collapsed::after {
    transform: rotate(-90deg);
}

.mobile-menu .dropdown-menu {
    padding-left: 20px;
    display: none;
    background: #f9f9f9;
    position: static;
    border: none;
    box-shadow: none;
    margin-top: 0;
    transition: all 0.3s ease;
    padding: 0;
}

.mobile-menu .dropdown-menu.show {
    display: block;
    animation: slideDown 0.3s ease;
    background: #f5f5f5;
    border-radius: 4px;
    margin-top: 5px;
    padding: 10px 0;
}

.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 991.98px) {
    .navbar-collapse {
        display: none;
    }
    
    .mobile-menu-btn {
        display: block;
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
    }

}

.mobile-menu.active {
    right: 0;
}

.mobile-menu .nav-item {
    border-bottom: 1px solid #eee;
}

.mobile-menu .dropdown-toggle::after {
    display: inline-block;
    margin-left: 5px;
    transition: transform 0.3s ease;
}

.mobile-menu .dropdown-toggle.collapsed::after {
    transform: rotate(-90deg);
}

.mobile-menu .dropdown-menu {
    padding-left: 20px;
    display: none;
}

.mobile-menu .dropdown-menu.show {
    display: block;
}

.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Top Bar */
.top-bar {
    background: var(--primary-color);
    color: white;
    padding: 12px 0;
    font-size: 14px;
    border-bottom: 2px solid rgba(255,255,255,0.1);
    position: relative;
    z-index: 2000;
}

/* Language selector removed */

.top-bar .language-selector {
    position: relative;
    z-index: 1001;
}

.top-bar .language-selector .dropdown-toggle {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    border-radius: 20px;
    padding: 5px 15px;
    transition: all 0.3s ease;
}

.top-bar .language-selector .dropdown-toggle:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.top-bar .language-selector .dropdown-toggle i {
    margin-right: 5px;
}

.top-bar .language-selector .dropdown-menu {
    border: none;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-top: 8px;
    min-width: 120px;
}

.top-bar .language-selector .dropdown-menu .dropdown-item {
    padding: 8px 15px;
    color: var(--text-color);
    transition: all 0.2s ease;
}

.top-bar .language-selector .dropdown-menu .dropdown-item:hover {
    background: var(--light-color);
    color: var(--primary-color);
}
.lead.mb-0.aos-init.aos-animate {
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}



.top-bar a {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
    font-size: 0.9rem;
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}


.top-bar a:hover {
    color: rgba(255,255,255,0.8);
}

.pagination {
    display: flex !important;
    justify-content: center;
    margin: 4rem auto 4rem auto;
    list-style: none;
    padding: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.pagination li {
    margin: 0 5px;
}

.pagination a {
    display: inline-block;
    padding: 8px 16px;
    color: var(--text-color);
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: var(--light-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pagination .active a {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .disabled a {
    color: #ccc;
    pointer-events: none;
    background: #f8f9fa;
}
