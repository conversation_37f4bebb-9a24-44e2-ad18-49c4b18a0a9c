<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系我们 - 武汉市金鸿祺科技有限公司</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation CSS -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top" style="background: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <div class="container">
            <a class="navbar-brand" href="index.html" style="font-weight: bold; color: var(--secondary-color); font-size: 1.5rem;">
                <i class="fas fa-industry me-2" style="color: var(--primary-color);"></i>
                金鸿祺科技
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.html">产品中心</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="service.html">解决方案</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="news.html">新闻资讯</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">关于我们</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="contact.html">联系我们</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <section class="breadcrumb-section" style="padding: 120px 0 60px; background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%); color: white;">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <nav aria-label="breadcrumb" data-aos="fade-up">
                        <ol class="breadcrumb mb-3" style="background: transparent;">
                            <li class="breadcrumb-item"><a href="index.html" style="color: rgba(255,255,255,0.8);">首页</a></li>
                            <li class="breadcrumb-item active" aria-current="page" style="color: white;">联系我们</li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-3" data-aos="fade-up" data-aos-delay="100" id="product-title">联系我们</h1>
                    <p class="lead mb-0" data-aos="fade-up" data-aos-delay="200">我们期待与您的合作，为您提供专业的焊接解决方案</p>
                </div>
            </div>
        </div>
    </section>

    <!---联系我们-->
    <section class="contact-section" style="padding: 80px 0;">
        <div class="container">
            <!-- 第一行：联系信息卡片 -->
            <div class="row mb-5">
                <!-- 公司地址卡片 -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="contact-card text-center" style="background: white; border-radius: 15px; padding: 30px 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); height: 100%; transition: all 0.3s ease;" data-aos="fade-up">
                        <div class="contact-icon mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                            <i class="fas fa-map-marker-alt" style="font-size: 1.8rem; color: white;"></i>
                        </div>
                        <h5 style="color: var(--secondary-color); margin-bottom: 15px; font-weight: 600;">公司地址</h5>
                        <p style="color: #666; line-height: 1.6; margin: 0; font-size: 0.95rem;">
                            湖北省武汉市东西湖区<br>
                            金银湖街道工业园区
                        </p>
                    </div>
                </div>

                <!-- 联系电话卡片 -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="contact-card text-center" style="background: white; border-radius: 15px; padding: 30px 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); height: 100%; transition: all 0.3s ease;" data-aos="fade-up" data-aos-delay="100">
                        <div class="contact-icon mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                            <i class="fas fa-phone" style="font-size: 1.8rem; color: white;"></i>
                        </div>
                        <h5 style="color: var(--secondary-color); margin-bottom: 15px; font-weight: 600;">联系电话</h5>
                        <p style="color: #666; line-height: 1.6; margin: 0; font-size: 0.95rem;">
                            销售热线<br>
                            <a href="tel:027-8888-8888" style="color: var(--primary-color); text-decoration: none; font-weight: 600;">027-8888-8888</a>
                        </p>
                    </div>
                </div>

                <!-- 电子邮箱卡片 -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="contact-card text-center" style="background: white; border-radius: 15px; padding: 30px 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); height: 100%; transition: all 0.3s ease;" data-aos="fade-up" data-aos-delay="200">
                        <div class="contact-icon mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                            <i class="fas fa-envelope" style="font-size: 1.8rem; color: white;"></i>
                        </div>
                        <h5 style="color: var(--secondary-color); margin-bottom: 15px; font-weight: 600;">电子邮箱</h5>
                        <p style="color: #666; line-height: 1.6; margin: 0; font-size: 0.95rem;">
                            商务合作<br>
                            <a href="mailto:<EMAIL>" style="color: var(--primary-color); text-decoration: none; font-weight: 600;"><EMAIL></a>
                        </p>
                    </div>
                </div>

                <!-- 工作时间卡片 -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="contact-card text-center" style="background: white; border-radius: 15px; padding: 30px 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); height: 100%; transition: all 0.3s ease;" data-aos="fade-up" data-aos-delay="300">
                        <div class="contact-icon mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                            <i class="fas fa-clock" style="font-size: 1.8rem; color: white;"></i>
                        </div>
                        <h5 style="color: var(--secondary-color); margin-bottom: 15px; font-weight: 600;">工作时间</h5>
                        <p style="color: #666; line-height: 1.6; margin: 0; font-size: 0.95rem;">
                            周一至周五<br>
                            <span style="color: var(--primary-color); font-weight: 600;">8:00 - 18:00</span>
                        </p>
                    </div>
                </div>
            </div>

            <!-- 第二行：联系表单区域 -->
            <div class="row">
                <!-- 左侧图片 -->
                <div class="col-lg-6 mb-4">
                    <div class="contact-image-card" style="background: white; border-radius: 15px; padding: 0; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden; height: 100%;" data-aos="fade-right">
                        <div class="contact-image" style="height: 100%; min-height: 500px; background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.3)), url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80'); background-size: cover; background-position: center; display: flex; align-items: center; justify-content: center; position: relative;">
                            <!-- 武汉黄鹤楼风景图内容 -->
                            <div class="image-content" style="text-align: center; color: white; z-index: 2;">
                                <div class="contact-visual" style="background: rgba(255,255,255,0.15); border-radius: 20px; padding: 50px 40px; backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.2);">
                                    <div class="location-icon mb-3">
                                        <i class="fas fa-map-marker-alt" style="font-size: 3.5rem; margin-bottom: 20px; color: #FFD700; text-shadow: 0 2px 10px rgba(255, 215, 0, 0.5);"></i>
                                    </div>
                                    <h3 style="color: white; margin-bottom: 20px; font-weight: 700; font-size: 2rem; text-shadow: 0 2px 10px rgba(0,0,0,0.5);">武汉金鸿祺科技</h3>
                                    <div class="location-info" style="margin-bottom: 25px;">
                                        <p style="color: rgba(255,255,255,0.95); line-height: 1.8; margin: 0; font-size: 1.1rem; text-shadow: 0 1px 5px rgba(0,0,0,0.3);">
                                            <i class="fas fa-building me-2" style="color: #FFD700;"></i>
                                            立足江城武汉，服务全国客户<br>
                                            <i class="fas fa-industry me-2" style="color: #FFD700;"></i>
                                            专业焊接设备制造商<br>
                                            <i class="fas fa-handshake me-2" style="color: #FFD700;"></i>
                                            期待与您携手合作
                                        </p>
                                    </div>
                                    <div class="contact-highlight" style="background: rgba(255, 215, 0, 0.2); border-radius: 10px; padding: 15px; border: 1px solid rgba(255, 215, 0, 0.3);">
                                        <p style="color: #FFD700; margin: 0; font-weight: 600; font-size: 1rem;">
                                            <i class="fas fa-phone me-2"></i>027-8888-8888
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- 装饰元素 -->
                            <div class="decoration-circles">
                                <div style="position: absolute; top: 20px; right: 20px; width: 100px; height: 100px; border: 2px solid rgba(255,255,255,0.2); border-radius: 50%; animation: float 6s ease-in-out infinite;"></div>
                                <div style="position: absolute; bottom: 30px; left: 30px; width: 60px; height: 60px; border: 2px solid rgba(255,255,255,0.3); border-radius: 50%; animation: float 4s ease-in-out infinite reverse;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧联系表单 -->
                <div class="col-lg-6 mb-4">
                    <div class="contact-form-card" style="background: white; border-radius: 15px; padding: 40px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); height: 100%;" data-aos="fade-left">
                        <div class="form-header text-center mb-4">
                            <h3 style="color: var(--secondary-color); margin-bottom: 15px; font-weight: 600;">
                                <i class="fas fa-paper-plane me-2" style="color: var(--primary-color);"></i>
                                在线留言
                            </h3>
                            <p style="color: #666; margin: 0;">请填写以下信息，我们会尽快与您联系</p>
                        </div>

                        <form id="contactForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label" style="color: #666; font-weight: 500;">
                                        <i class="fas fa-user me-1" style="color: var(--primary-color);"></i>姓名 *
                                    </label>
                                    <input type="text" class="form-control" id="name" required style="border-radius: 10px; border: 2px solid #eee; padding: 12px 15px; transition: all 0.3s ease;">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label" style="color: #666; font-weight: 500;">
                                        <i class="fas fa-phone me-1" style="color: var(--primary-color);"></i>联系电话 *
                                    </label>
                                    <input type="tel" class="form-control" id="phone" required style="border-radius: 10px; border: 2px solid #eee; padding: 12px 15px; transition: all 0.3s ease;">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label" style="color: #666; font-weight: 500;">
                                    <i class="fas fa-envelope me-1" style="color: var(--primary-color);"></i>邮箱地址
                                </label>
                                <input type="email" class="form-control" id="email" style="border-radius: 10px; border: 2px solid #eee; padding: 12px 15px; transition: all 0.3s ease;">
                            </div>
                            <div class="mb-3">
                                <label for="company" class="form-label" style="color: #666; font-weight: 500;">
                                    <i class="fas fa-building me-1" style="color: var(--primary-color);"></i>公司名称
                                </label>
                                <input type="text" class="form-control" id="company" style="border-radius: 10px; border: 2px solid #eee; padding: 12px 15px; transition: all 0.3s ease;">
                            </div>
                            <div class="mb-3">
                                <label for="subject" class="form-label" style="color: #666; font-weight: 500;">
                                    <i class="fas fa-tags me-1" style="color: var(--primary-color);"></i>咨询类型
                                </label>
                                <select class="form-select" id="subject" style="border-radius: 10px; border: 2px solid #eee; padding: 12px 15px; transition: all 0.3s ease;">
                                    <option value="">请选择咨询类型</option>
                                    <option value="产品咨询">产品咨询</option>
                                    <option value="技术支持">技术支持</option>
                                    <option value="售后服务">售后服务</option>
                                    <option value="商务合作">商务合作</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                            <div class="mb-4">
                                <label for="message" class="form-label" style="color: #666; font-weight: 500;">
                                    <i class="fas fa-comment-dots me-1" style="color: var(--primary-color);"></i>留言内容 *
                                </label>
                                <textarea class="form-control" id="message" rows="4" required style="border-radius: 10px; border: 2px solid #eee; padding: 12px 15px; transition: all 0.3s ease; resize: vertical;" placeholder="请详细描述您的需求或问题..."></textarea>
                            </div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); border: none; border-radius: 25px; padding: 15px 50px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);">
                                    <i class="fas fa-paper-plane me-2"></i>发送留言
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!---联系我们-->

    <!-- Footer -->
    <footer style="background: var(--secondary-color); color: white; padding: 60px 0 30px;">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5 class="mb-3">武汉市金鸿祺科技有限公司</h5>
                    <p class="mb-3">专业从事汽车焊装设备的研发、生产和销售，为客户提供全方位的焊接解决方案。</p>
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-weixin"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-weibo"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-qq"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">快速链接</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.html" class="text-white-50">首页</a></li>
                        <li><a href="products.html" class="text-white-50">产品中心</a></li>
                        <li><a href="service.html" class="text-white-50">解决方案</a></li>
                        <li><a href="about.html" class="text-white-50">关于我们</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="mb-3">联系信息</h6>
                    <p class="mb-2"><i class="fas fa-map-marker-alt me-2"></i>武汉市东西湖区</p>
                    <p class="mb-2"><i class="fas fa-phone me-2"></i>027-8888-8888</p>
                    <p class="mb-2"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="mb-3">服务时间</h6>
                    <p class="mb-2">周一至周五：8:00-18:00</p>
                    <p class="mb-2">周六：9:00-17:00</p>
                    <p class="mb-0">周日：休息</p>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 武汉市金鸿祺科技有限公司. 保留所有权利.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-white-50 me-3">隐私政策</a>
                    <a href="#" class="text-white-50">使用条款</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTop" class="back-to-top" title="返回顶部">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- Custom JS -->
    <script src="css/script.js"></script>

    <!-- 联系页面自定义样式和脚本 -->
    <style>
        /* 卡片悬停效果 */
        .contact-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
        }

        .contact-card:hover .contact-icon {
            transform: scale(1.1);
        }

        /* 表单样式 */
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25) !important;
        }

        .form-control:hover, .form-select:hover {
            border-color: var(--primary-color);
        }

        /* 按钮效果 */
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.4) !important;
        }

        /* 浮动动画 */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        /* 左侧图片区域样式 */
        .contact-image {
            position: relative;
            overflow: hidden;
        }

        .contact-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.3));
            z-index: 1;
        }

        .contact-visual {
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .contact-visual:hover {
            transform: translateY(-5px);
            background: rgba(255,255,255,0.2) !important;
        }

        .location-icon i {
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.8;
            }
        }

        .contact-highlight {
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
            }
            to {
                box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
            }
        }

        .decoration-circles div {
            z-index: 2;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .contact-card {
                padding: 25px 15px !important;
                margin-bottom: 20px;
            }

            .contact-icon {
                width: 60px !important;
                height: 60px !important;
            }

            .contact-icon i {
                font-size: 1.5rem !important;
            }

            .contact-form-card {
                padding: 30px 20px !important;
            }

            .contact-image {
                min-height: 300px !important;
            }

            .contact-visual {
                padding: 30px 20px !important;
            }

            .contact-visual h3 {
                font-size: 1.5rem !important;
            }

            .contact-visual i {
                font-size: 3rem !important;
            }

            .decoration-circles div {
                display: none;
            }
        }

        @media (max-width: 576px) {
            .row.mb-5 .col-lg-3 {
                margin-bottom: 20px !important;
            }

            .contact-card h5 {
                font-size: 1.1rem;
            }

            .contact-card p {
                font-size: 0.9rem;
            }
        }
    </style>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 联系表单提交处理
            document.getElementById('contactForm').addEventListener('submit', function(e) {
                e.preventDefault();

                // 获取表单数据
                const name = document.getElementById('name').value.trim();
                const phone = document.getElementById('phone').value.trim();
                const email = document.getElementById('email').value.trim();
                const company = document.getElementById('company').value.trim();
                const subject = document.getElementById('subject').value;
                const message = document.getElementById('message').value.trim();

                // 表单验证
                if (!name) {
                    alert('请填写您的姓名！');
                    document.getElementById('name').focus();
                    return;
                }

                if (!phone) {
                    alert('请填写联系电话！');
                    document.getElementById('phone').focus();
                    return;
                }

                if (phone.length < 11) {
                    alert('请填写正确的手机号码！');
                    document.getElementById('phone').focus();
                    return;
                }

                if (!message) {
                    alert('请填写留言内容！');
                    document.getElementById('message').focus();
                    return;
                }

                // 邮箱格式验证（如果填写了邮箱）
                if (email && !isValidEmail(email)) {
                    alert('请填写正确的邮箱地址！');
                    document.getElementById('email').focus();
                    return;
                }

                // 模拟提交成功
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;

                // 显示提交中状态
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>提交中...';
                submitBtn.disabled = true;

                // 模拟网络请求延迟
                setTimeout(() => {
                    alert('感谢您的留言！我们会在24小时内与您联系。');

                    // 重置表单
                    this.reset();

                    // 恢复按钮状态
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 1500);
            });

            // 电话号码格式化
            document.getElementById('phone').addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length > 11) {
                    value = value.slice(0, 11);
                }
                e.target.value = value;
            });

            // 表单字段焦点效果
            const formInputs = document.querySelectorAll('.form-control, .form-select');
            formInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateY(-2px)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                });
            });
        });

        // 邮箱格式验证函数
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
    </script>

</body>
</html>
