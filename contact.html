<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系我们 - 武汉市金鸿祺科技有限公司</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation CSS -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top" style="background: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <div class="container">
            <a class="navbar-brand" href="index.html" style="font-weight: bold; color: var(--secondary-color); font-size: 1.5rem;">
                <i class="fas fa-industry me-2" style="color: var(--primary-color);"></i>
                金鸿祺科技
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.html">产品中心</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="service.html">解决方案</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="news.html">新闻资讯</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">关于我们</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="contact.html">联系我们</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <section class="breadcrumb-section" style="padding: 120px 0 60px; background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%); color: white;">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <nav aria-label="breadcrumb" data-aos="fade-up">
                        <ol class="breadcrumb mb-3" style="background: transparent;">
                            <li class="breadcrumb-item"><a href="index.html" style="color: rgba(255,255,255,0.8);">首页</a></li>
                            <li class="breadcrumb-item active" aria-current="page" style="color: white;">联系我们</li>
                        </ol>
                    </nav>
                    <h1 class="display-4 fw-bold mb-3" data-aos="fade-up" data-aos-delay="100" id="product-title">联系我们</h1>
                    <p class="lead mb-0" data-aos="fade-up" data-aos-delay="200">我们期待与您的合作，为您提供专业的焊接解决方案</p>
                </div>
            </div>
        </div>
    </section>

    <!---联系我们-->
    <section class="contact-section" style="padding: 80px 0;">
        <div class="container">
            <div class="row">
                <!-- 联系信息卡片 -->
                <div class="col-lg-4 mb-4">
                    <div class="contact-info-card" style="background: white; border-radius: 15px; padding: 40px 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); height: 100%;" data-aos="fade-up">
                        <div class="text-center mb-4">
                            <div class="contact-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px;">
                                <i class="fas fa-map-marker-alt" style="font-size: 2rem; color: white;"></i>
                            </div>
                            <h4 style="color: var(--secondary-color); margin-bottom: 15px;">公司地址</h4>
                        </div>
                        <div class="contact-details">
                            <p style="color: #666; line-height: 1.8; margin-bottom: 10px;">
                                <strong>总部地址：</strong><br>
                                湖北省武汉市东西湖区金银湖街道<br>
                                金山大道沿线工业园区A区
                            </p>
                            <p style="color: #666; line-height: 1.8; margin-bottom: 10px;">
                                <strong>生产基地：</strong><br>
                                武汉市东西湖区临空港经济技术开发区<br>
                                智能制造产业园B栋
                            </p>
                            <p style="color: #666; line-height: 1.8;">
                                <strong>邮政编码：</strong>430040
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 联系方式卡片 -->
                <div class="col-lg-4 mb-4">
                    <div class="contact-info-card" style="background: white; border-radius: 15px; padding: 40px 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); height: 100%;" data-aos="fade-up" data-aos-delay="200">
                        <div class="text-center mb-4">
                            <div class="contact-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px;">
                                <i class="fas fa-phone" style="font-size: 2rem; color: white;"></i>
                            </div>
                            <h4 style="color: var(--secondary-color); margin-bottom: 15px;">联系电话</h4>
                        </div>
                        <div class="contact-details">
                            <p style="color: #666; line-height: 1.8; margin-bottom: 15px;">
                                <strong>销售热线：</strong><br>
                                <a href="tel:027-8888-8888" style="color: var(--primary-color); text-decoration: none; font-size: 1.2rem; font-weight: bold;">027-8888-8888</a>
                            </p>
                            <p style="color: #666; line-height: 1.8; margin-bottom: 15px;">
                                <strong>技术支持：</strong><br>
                                <a href="tel:027-8888-8889" style="color: var(--primary-color); text-decoration: none;">027-8888-8889</a>
                            </p>
                            <p style="color: #666; line-height: 1.8; margin-bottom: 15px;">
                                <strong>售后服务：</strong><br>
                                <a href="tel:************" style="color: var(--primary-color); text-decoration: none;">************</a>
                            </p>
                            <p style="color: #666; line-height: 1.8;">
                                <strong>传真：</strong>027-8888-8890
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 在线联系卡片 -->
                <div class="col-lg-4 mb-4">
                    <div class="contact-info-card" style="background: white; border-radius: 15px; padding: 40px 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); height: 100%;" data-aos="fade-up" data-aos-delay="400">
                        <div class="text-center mb-4">
                            <div class="contact-icon" style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px;">
                                <i class="fas fa-envelope" style="font-size: 2rem; color: white;"></i>
                            </div>
                            <h4 style="color: var(--secondary-color); margin-bottom: 15px;">在线联系</h4>
                        </div>
                        <div class="contact-details">
                            <p style="color: #666; line-height: 1.8; margin-bottom: 15px;">
                                <strong>商务邮箱：</strong><br>
                                <a href="mailto:<EMAIL>" style="color: var(--primary-color); text-decoration: none;"><EMAIL></a>
                            </p>
                            <p style="color: #666; line-height: 1.8; margin-bottom: 15px;">
                                <strong>技术邮箱：</strong><br>
                                <a href="mailto:<EMAIL>" style="color: var(--primary-color); text-decoration: none;"><EMAIL></a>
                            </p>
                            <p style="color: #666; line-height: 1.8; margin-bottom: 15px;">
                                <strong>官方网站：</strong><br>
                                <a href="http://www.jhqtech.com" style="color: var(--primary-color); text-decoration: none;">www.jhqtech.com</a>
                            </p>
                            <div class="social-contact" style="margin-top: 20px;">
                                <p style="color: #666; margin-bottom: 10px;"><strong>社交媒体：</strong></p>
                                <div class="social-links">
                                    <a href="#" class="btn btn-outline-primary btn-sm me-2" style="border-radius: 25px;">
                                        <i class="fab fa-weixin me-1"></i>微信
                                    </a>
                                    <a href="#" class="btn btn-outline-primary btn-sm me-2" style="border-radius: 25px;">
                                        <i class="fab fa-qq me-1"></i>QQ
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 联系表单和地图 -->
            <div class="row mt-5">
                <!-- 联系表单 -->
                <div class="col-lg-6 mb-4">
                    <div class="contact-form-card" style="background: white; border-radius: 15px; padding: 40px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);" data-aos="fade-right">
                        <h3 style="color: var(--secondary-color); margin-bottom: 30px; text-align: center;">
                            <i class="fas fa-paper-plane me-2" style="color: var(--primary-color);"></i>
                            在线留言
                        </h3>
                        <form id="contactForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label" style="color: #666; font-weight: 500;">姓名 *</label>
                                    <input type="text" class="form-control" id="name" required style="border-radius: 10px; border: 2px solid #eee; padding: 12px 15px;">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label" style="color: #666; font-weight: 500;">联系电话 *</label>
                                    <input type="tel" class="form-control" id="phone" required style="border-radius: 10px; border: 2px solid #eee; padding: 12px 15px;">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label" style="color: #666; font-weight: 500;">邮箱</label>
                                <input type="email" class="form-control" id="email" style="border-radius: 10px; border: 2px solid #eee; padding: 12px 15px;">
                            </div>
                            <div class="mb-3">
                                <label for="company" class="form-label" style="color: #666; font-weight: 500;">公司名称</label>
                                <input type="text" class="form-control" id="company" style="border-radius: 10px; border: 2px solid #eee; padding: 12px 15px;">
                            </div>
                            <div class="mb-3">
                                <label for="subject" class="form-label" style="color: #666; font-weight: 500;">咨询类型</label>
                                <select class="form-select" id="subject" style="border-radius: 10px; border: 2px solid #eee; padding: 12px 15px;">
                                    <option value="">请选择咨询类型</option>
                                    <option value="产品咨询">产品咨询</option>
                                    <option value="技术支持">技术支持</option>
                                    <option value="售后服务">售后服务</option>
                                    <option value="商务合作">商务合作</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                            <div class="mb-4">
                                <label for="message" class="form-label" style="color: #666; font-weight: 500;">留言内容 *</label>
                                <textarea class="form-control" id="message" rows="5" required style="border-radius: 10px; border: 2px solid #eee; padding: 12px 15px;" placeholder="请详细描述您的需求..."></textarea>
                            </div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); border: none; border-radius: 25px; padding: 12px 40px; font-weight: 500;">
                                    <i class="fas fa-paper-plane me-2"></i>发送留言
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 地图和工作时间 -->
                <div class="col-lg-6 mb-4">
                    <div class="map-card" style="background: white; border-radius: 15px; padding: 40px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); height: 100%;" data-aos="fade-left">
                        <h3 style="color: var(--secondary-color); margin-bottom: 30px; text-align: center;">
                            <i class="fas fa-map-marked-alt me-2" style="color: var(--primary-color);"></i>
                            位置导航
                        </h3>

                        <!-- 地图容器 -->
                        <div class="map-container" style="height: 300px; border-radius: 10px; overflow: hidden; margin-bottom: 30px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; border: 2px solid #eee;">
                            <div class="map-placeholder" style="text-align: center; color: #666;">
                                <i class="fas fa-map-marker-alt" style="font-size: 3rem; color: var(--primary-color); margin-bottom: 15px;"></i>
                                <p style="margin: 0; font-size: 1.1rem;">地图加载中...</p>
                                <p style="margin: 5px 0 0; font-size: 0.9rem; color: #999;">武汉市东西湖区金银湖街道</p>
                            </div>
                        </div>

                        <!-- 工作时间 -->
                        <div class="work-time" style="background: #f8f9fa; border-radius: 10px; padding: 20px;">
                            <h5 style="color: var(--secondary-color); margin-bottom: 15px;">
                                <i class="fas fa-clock me-2" style="color: var(--primary-color);"></i>
                                工作时间
                            </h5>
                            <div class="time-list">
                                <div class="time-item" style="display: flex; justify-content: space-between; margin-bottom: 8px; padding: 5px 0;">
                                    <span style="color: #666;">周一至周五</span>
                                    <span style="color: var(--primary-color); font-weight: 500;">8:00 - 18:00</span>
                                </div>
                                <div class="time-item" style="display: flex; justify-content: space-between; margin-bottom: 8px; padding: 5px 0;">
                                    <span style="color: #666;">周六</span>
                                    <span style="color: var(--primary-color); font-weight: 500;">9:00 - 17:00</span>
                                </div>
                                <div class="time-item" style="display: flex; justify-content: space-between; padding: 5px 0;">
                                    <span style="color: #666;">周日</span>
                                    <span style="color: #999;">休息</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!---联系我们-->

    <!-- Footer -->
    <footer style="background: var(--secondary-color); color: white; padding: 60px 0 30px;">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5 class="mb-3">武汉市金鸿祺科技有限公司</h5>
                    <p class="mb-3">专业从事汽车焊装设备的研发、生产和销售，为客户提供全方位的焊接解决方案。</p>
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-weixin"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-weibo"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-qq"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3">快速链接</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.html" class="text-white-50">首页</a></li>
                        <li><a href="products.html" class="text-white-50">产品中心</a></li>
                        <li><a href="service.html" class="text-white-50">解决方案</a></li>
                        <li><a href="about.html" class="text-white-50">关于我们</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="mb-3">联系信息</h6>
                    <p class="mb-2"><i class="fas fa-map-marker-alt me-2"></i>武汉市东西湖区</p>
                    <p class="mb-2"><i class="fas fa-phone me-2"></i>027-8888-8888</p>
                    <p class="mb-2"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="mb-3">服务时间</h6>
                    <p class="mb-2">周一至周五：8:00-18:00</p>
                    <p class="mb-2">周六：9:00-17:00</p>
                    <p class="mb-0">周日：休息</p>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 武汉市金鸿祺科技有限公司. 保留所有权利.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-white-50 me-3">隐私政策</a>
                    <a href="#" class="text-white-50">使用条款</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTop" class="back-to-top" title="返回顶部">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- Custom JS -->
    <script src="css/script.js"></script>

    <!-- 联系页面自定义样式和脚本 -->
    <style>
        .contact-info-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
            transition: all 0.3s ease;
        }

        .contact-icon {
            transition: all 0.3s ease;
        }

        .contact-info-card:hover .contact-icon {
            transform: scale(1.1);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25) !important;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
        }

        .social-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        @media (max-width: 768px) {
            .contact-info-card {
                padding: 30px 20px !important;
            }

            .contact-form-card, .map-card {
                padding: 30px 20px !important;
            }

            .contact-icon {
                width: 60px !important;
                height: 60px !important;
            }

            .contact-icon i {
                font-size: 1.5rem !important;
            }
        }
    </style>

    <script>
        // 联系表单提交处理
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // 获取表单数据
            const name = document.getElementById('name').value;
            const phone = document.getElementById('phone').value;
            const email = document.getElementById('email').value;
            const company = document.getElementById('company').value;
            const subject = document.getElementById('subject').value;
            const message = document.getElementById('message').value;

            // 简单验证
            if (!name || !phone || !message) {
                alert('请填写必填项目！');
                return;
            }

            // 模拟提交成功
            alert('感谢您的留言！我们会在24小时内与您联系。');

            // 重置表单
            this.reset();
        });

        // 电话号码格式化
        document.getElementById('phone').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 11) {
                value = value.slice(0, 11);
            }
            e.target.value = value;
        });

        // 模拟地图加载
        setTimeout(function() {
            const mapContainer = document.querySelector('.map-placeholder');
            if (mapContainer) {
                mapContainer.innerHTML = `
                    <div style="text-align: center; color: #666;">
                        <i class="fas fa-map-marker-alt" style="font-size: 3rem; color: var(--primary-color); margin-bottom: 15px;"></i>
                        <p style="margin: 0; font-size: 1.1rem; font-weight: 500;">武汉市金鸿祺科技有限公司</p>
                        <p style="margin: 5px 0 0; font-size: 0.9rem; color: #999;">湖北省武汉市东西湖区金银湖街道</p>
                        <button class="btn btn-outline-primary btn-sm mt-2" onclick="window.open('https://map.baidu.com')" style="border-radius: 20px;">
                            <i class="fas fa-external-link-alt me-1"></i>查看详细地图
                        </button>
                    </div>
                `;
            }
        }, 2000);
    </script>

</body>
</html>
